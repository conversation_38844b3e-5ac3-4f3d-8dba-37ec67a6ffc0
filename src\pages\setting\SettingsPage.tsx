import React from 'react';
import { 
  Typography, 
  Card, 
  Form, 
  Input, 
  Switch, 
  Button, 
  Select, 
  Divider,
  Row,
  Col,
  Space,
  Alert,
  InputNumber,
  Upload
} from 'antd';
import { 
  SaveOutlined, 
  UploadOutlined,
  GlobalOutlined,
  SecurityScanOutlined,
  NotificationOutlined,
  SettingOutlined
} from '@ant-design/icons';
import type { UploadProps } from 'antd';

const { Title, Text } = Typography;
const { Option } = Select;

const SettingsPage: React.FC = () => {
  const [generalForm] = Form.useForm();
  const [securityForm] = Form.useForm();
  const [notificationForm] = Form.useForm();

  const uploadProps: UploadProps = {
    name: 'file',
    action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
    headers: {
      authorization: 'authorization-text',
    },
    onChange(info) {
      console.log(info.fileList);
    },
  };

  const handleGeneralSave = (values: unknown) => {
    console.log('General settings:', values);
  };

  const handleSecuritySave = (values: unknown) => {
    console.log('Security settings:', values);
  };

  const handleNotificationSave = (values: unknown) => {
    console.log('Notification settings:', values);
  };

  return (
    <div>
      <Title level={2}>Settings</Title>
      <Text type="secondary" style={{ display: 'block', marginBottom: 24 }}>
        Manage your system configuration and preferences
      </Text>

      <Row gutter={[24, 24]}>
        {/* General Settings */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <GlobalOutlined />
                General Settings
              </Space>
            }
          >
            <Form
              form={generalForm}
              layout="vertical"
              onFinish={handleGeneralSave}
              initialValues={{
                systemName: 'Chatbot Admin',
                language: 'en',
                timezone: 'UTC+7',
                autoSave: true,
                maintenanceMode: false,
                maxConcurrentUsers: 1000,
                sessionTimeout: 30,
              }}
            >
              <Form.Item
                name="systemName"
                label="System Name"
                rules={[{ required: true, message: 'Please enter system name' }]}
              >
                <Input placeholder="Enter system name" />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="language"
                    label="Language"
                  >
                    <Select>
                      <Option value="en">English</Option>
                      <Option value="vi">Tiếng Việt</Option>
                      <Option value="ja">日本語</Option>
                      <Option value="ko">한국어</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="timezone"
                    label="Timezone"
                  >
                    <Select>
                      <Option value="UTC+7">UTC+7 (Asia/Ho_Chi_Minh)</Option>
                      <Option value="UTC+0">UTC+0 (GMT)</Option>
                      <Option value="UTC-5">UTC-5 (EST)</Option>
                      <Option value="UTC+9">UTC+9 (JST)</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="maxConcurrentUsers"
                    label="Max Concurrent Users"
                  >
                    <InputNumber min={1} max={10000} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="sessionTimeout"
                    label="Session Timeout (minutes)"
                  >
                    <InputNumber min={5} max={480} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="autoSave"
                label="Auto Save"
                valuePropName="checked"
              >
                <Switch checkedChildren="Enabled" unCheckedChildren="Disabled" />
              </Form.Item>

              <Form.Item
                name="maintenanceMode"
                label="Maintenance Mode"
                valuePropName="checked"
              >
                <Switch checkedChildren="ON" unCheckedChildren="OFF" />
              </Form.Item>

              <Form.Item>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                  Save General Settings
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* Security Settings */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <SecurityScanOutlined />
                Security Settings
              </Space>
            }
          >
            <Form
              form={securityForm}
              layout="vertical"
              onFinish={handleSecuritySave}
              initialValues={{
                twoFactorAuth: true,
                passwordExpiry: 90,
                loginAttempts: 5,
                ipWhitelist: false,
                dataEncryption: true,
                auditLog: true,
              }}
            >
              <Alert
                message="Security Warning"
                description="Changing security settings may affect system access. Please review carefully."
                type="warning"
                style={{ marginBottom: 16 }}
                showIcon
              />

              <Form.Item
                name="twoFactorAuth"
                label="Two-Factor Authentication"
                valuePropName="checked"
              >
                <Switch checkedChildren="Enabled" unCheckedChildren="Disabled" />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="passwordExpiry"
                    label="Password Expiry (days)"
                  >
                    <InputNumber min={30} max={365} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="loginAttempts"
                    label="Max Login Attempts"
                  >
                    <InputNumber min={3} max={10} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="ipWhitelist"
                label="IP Whitelist"
                valuePropName="checked"
              >
                <Switch checkedChildren="Enabled" unCheckedChildren="Disabled" />
              </Form.Item>

              <Form.Item
                name="dataEncryption"
                label="Data Encryption"
                valuePropName="checked"
              >
                <Switch checkedChildren="Enabled" unCheckedChildren="Disabled" />
              </Form.Item>

              <Form.Item
                name="auditLog"
                label="Audit Logging"
                valuePropName="checked"
              >
                <Switch checkedChildren="Enabled" unCheckedChildren="Disabled" />
              </Form.Item>

              <Form.Item>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                  Save Security Settings
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* Notification Settings */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <NotificationOutlined />
                Notification Settings
              </Space>
            }
          >
            <Form
              form={notificationForm}
              layout="vertical"
              onFinish={handleNotificationSave}
              initialValues={{
                emailNotifications: true,
                smsNotifications: false,
                pushNotifications: true,
                errorAlerts: true,
                systemUpdates: true,
                reportSchedule: 'daily',
              }}
            >
              <Form.Item
                name="emailNotifications"
                label="Email Notifications"
                valuePropName="checked"
              >
                <Switch checkedChildren="Enabled" unCheckedChildren="Disabled" />
              </Form.Item>

              <Form.Item
                name="smsNotifications"
                label="SMS Notifications"
                valuePropName="checked"
              >
                <Switch checkedChildren="Enabled" unCheckedChildren="Disabled" />
              </Form.Item>

              <Form.Item
                name="pushNotifications"
                label="Push Notifications"
                valuePropName="checked"
              >
                <Switch checkedChildren="Enabled" unCheckedChildren="Disabled" />
              </Form.Item>

              <Divider>Alert Types</Divider>

              <Form.Item
                name="errorAlerts"
                label="Error Alerts"
                valuePropName="checked"
              >
                <Switch checkedChildren="Enabled" unCheckedChildren="Disabled" />
              </Form.Item>

              <Form.Item
                name="systemUpdates"
                label="System Updates"
                valuePropName="checked"
              >
                <Switch checkedChildren="Enabled" unCheckedChildren="Disabled" />
              </Form.Item>

              <Form.Item
                name="reportSchedule"
                label="Report Schedule"
              >
                <Select>
                  <Option value="realtime">Real-time</Option>
                  <Option value="hourly">Hourly</Option>
                  <Option value="daily">Daily</Option>
                  <Option value="weekly">Weekly</Option>
                  <Option value="monthly">Monthly</Option>
                </Select>
              </Form.Item>

              <Form.Item>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                  Save Notification Settings
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* System Configuration */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <SettingOutlined />
                System Configuration
              </Space>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>Backup & Restore</Text>
                <div style={{ marginTop: 8 }}>
                  <Space>
                    <Button type="default">Create Backup</Button>
                    <Upload {...uploadProps}>
                      <Button icon={<UploadOutlined />}>Restore Backup</Button>
                    </Upload>
                  </Space>
                </div>
              </div>

              <Divider />

              <div>
                <Text strong>API Configuration</Text>
                <div style={{ marginTop: 8 }}>
                  <Form layout="vertical">
                    <Form.Item label="API Rate Limit (requests/minute)">
                      <InputNumber min={10} max={10000} defaultValue={1000} style={{ width: '100%' }} />
                    </Form.Item>
                    <Form.Item label="API Timeout (seconds)">
                      <InputNumber min={5} max={300} defaultValue={30} style={{ width: '100%' }} />
                    </Form.Item>
                  </Form>
                </div>
              </div>

              <Divider />

              <div>
                <Text strong>Database Settings</Text>
                <div style={{ marginTop: 8 }}>
                  <Form layout="vertical">
                    <Form.Item label="Connection Pool Size">
                      <InputNumber min={5} max={100} defaultValue={20} style={{ width: '100%' }} />
                    </Form.Item>
                    <Form.Item label="Query Timeout (seconds)">
                      <InputNumber min={10} max={300} defaultValue={60} style={{ width: '100%' }} />
                    </Form.Item>
                  </Form>
                </div>
              </div>

              <Button type="primary" icon={<SaveOutlined />} style={{ width: '100%' }}>
                Save System Configuration
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SettingsPage;