// Application configuration
export const APP_CONFIG = {
  // API Configuration
  USE_MOCK_API: import.meta.env.VITE_USE_MOCK_API === 'true' || false,
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',

  // Feature flags
  ENABLE_PRODUCT_API: import.meta.env.VITE_ENABLE_PRODUCT_API === 'true' || true,

  // Cloudinary Configuration
  CLOUDINARY_URL: import.meta.env.VITE_CLOUDINARY_URL || '',
  CLOUDINARY_UPLOAD_PRESET: import.meta.env.VITE_CLOUDINARY_UPLOAD_PRESET || 'ml_default',

  // Development settings
  DEBUG_MODE: import.meta.env.DEV || false,

  // Pagination defaults
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,

  // Upload settings
  MAX_IMAGE_SIZE: 5 * 1024 * 1024, // 5MB
  MAX_IMAGES_PER_PRODUCT: 5,
  SUPPORTED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
};

export default APP_CONFIG;
