import type { Product, ProductFormData } from '../types/product';
import productsData from '../data/products_only.json';

// Simulate API calls with localStorage for persistence
const STORAGE_KEY = 'products';

class ProductService {
  private products: Product[] = [];

  constructor() {
    this.loadProducts();
  }

  private loadProducts(): void {
    try {
      const storedProducts = localStorage.getItem(STORAGE_KEY);
      if (storedProducts) {
        this.products = JSON.parse(storedProducts);
      } else {
        // Initialize with data from JSON file and add IDs
        this.products = productsData.map((product, index) => ({
          ...product,
          id: index + 1, // Generate sequential IDs since JSON data doesn't have id field
          name: product.product_name // Map product_name to name for Product type compatibility
        }));
        this.saveProducts();
      }
    } catch (error) {
      console.error('Error loading products:', error);
      this.products = [];
    }
  }

  private saveProducts(): void {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.products));
    } catch (error) {
      console.error('Error saving products:', error);
    }
  }

  // Get all products
  async getAllProducts(): Promise<Product[]> {
    return new Promise((resolve) => {
      setTimeout(() => resolve([...this.products]), 100);
    });
  }

  // Get product by ID
  async getProductById(id: string): Promise<Product | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const product = this.products.find(p => String(p.id) === id);
        resolve(product || null);
      }, 100);
    });
  }

  // Create new product
  async createProduct(productData: ProductFormData): Promise<Product> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          const newProduct: Product = {
            ...productData,
            id: Date.now(),
            name: productData.product_name // Ensure 'name' is set for Product type compatibility
          };
          this.products.push(newProduct);
          this.saveProducts();
          resolve(newProduct);
        } catch (error) {
          reject(error);
        }
      }, 100);
    });
  }

  // Update product
  async updateProduct(id: number, productData: ProductFormData): Promise<Product> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          const index = this.products.findIndex(p => p.id === id);
          if (index === -1) {
            reject(new Error('Product not found'));
            return;
          }
          
          const updatedProduct: Product = {
            ...productData,
            id,
            name: productData.product_name // Ensure 'name' is set for Product type compatibility
          };
          
          this.products[index] = updatedProduct;
          this.saveProducts();
          resolve(updatedProduct);
        } catch (error) {
          reject(error);
        }
      }, 100);
    });
  }

  // Delete product
  async deleteProduct(id: number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          const index = this.products.findIndex(p => p.id === id);
          if (index === -1) {
            reject(new Error('Product not found'));
            return;
          }
          
          this.products.splice(index, 1);
          this.saveProducts();
          resolve(true);
        } catch (error) {
          reject(error);
        }
      }, 100);
    });
  }

  // Search products
  async searchProducts(query: string): Promise<Product[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const lowercaseQuery = query.toLowerCase();
        const filteredProducts = this.products.filter(product =>
          product.name.toLowerCase().includes(lowercaseQuery) ||
          product.notes?.toLowerCase().includes(lowercaseQuery) ||
          product.ingredients.toLowerCase().includes(lowercaseQuery)
        );
        resolve(filteredProducts);
      }, 100);
    });
  }
}

export const productService = new ProductService();
export default productService;
