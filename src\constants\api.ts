// API base URL and endpoints
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';

export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/api/auth/login',
    LOGOUT: '/api/auth/logout',
    REGISTER: '/api/auth/register',
    REFRESH: '/api/auth/refresh',
  },

  // Chatbot management
  CHATBOTS: {
    LIST: '/api/chatbots',
    CREATE: '/api/chatbots',
    GET: (id: string) => `/api/chatbots/${id}`,
    UPDATE: (id: string) => `/api/chatbots/${id}`,
    DELETE: (id: string) => `/api/chatbots/${id}`,
  },

  // User management
  USERS: {
    LIST: '/api/users',
    PROFILE: '/api/users/profile',
    UPDATE: (id: string) => `/api/users/${id}`,
  },

  // Product management
  PRODUCTS: {
    LIST: '/api/products',
    CREATE: '/api/products',
    GET: (id: string) => `/api/products/${id}`,
    UPDATE: (id: string) => `/api/products/${id}`,
    PATCH: (id: string) => `/api/products/${id}`,
    DELETE: (id: string) => `/api/products/${id}`,
    BULK_IMPORT: '/api/products/bulk-import',
  }
};

export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  DASHBOARD: '/dashboard',
  CHATBOTS: '/chatbots',
  CHATBOT_DETAIL: '/chatbots/:id',
  USERS: '/users',
  SETTINGS: '/settings',
} as const;

export const STATUS_CODES = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;
