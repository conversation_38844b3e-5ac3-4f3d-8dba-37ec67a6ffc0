import React, { useState, useEffect } from 'react';
import type { TablePaginationConfig, SorterResult, FilterValue } from 'antd/es/table/interface';
import {
  Table,
  Button,
  Space,
  Input,
  Modal,
  Typography,
  Tag,
  Image,
  Tooltip,
  Card,
  Pagination,
  Dropdown
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  EyeOutlined,
  FilterOutlined,
  DownOutlined,
  ClearOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { Product } from '../../types/product';
import { useProducts } from '../../hooks/useProducts';

const { Title } = Typography;
const { Search } = Input;

const Products: React.FC = () => {
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend'>('descend');
  const [filters, setFilters] = useState({
    hideNoImage: false,
    showOnlyNoImage: false,
    priceRange: 'all' as 'all' | 'free' | 'paid',
  });
  const navigate = useNavigate();

  const {
    products,
    loading,
    pagination,
    loadProducts,
    searchProducts,
    deleteProduct,
  } = useProducts(); // Remove initialParams to prevent re-initialization

  // Load products when component mounts or parameters change
  useEffect(() => {
    loadProducts({
      page: currentPage,
      limit: pageSize,
      sortBy,
      sortOrder: sortOrder === 'ascend' ? 'asc' : 'desc'
    });
  }, [currentPage, pageSize, sortBy, sortOrder, loadProducts]);

  const handleSearch = async (value: string) => {
    if (!value.trim()) {
      await loadProducts({ page: 1, limit: pageSize });
      setCurrentPage(1);
      return;
    }

    await searchProducts(value, { page: 1, limit: pageSize });
    setCurrentPage(1);
  };

  const handleDelete = (id: string | number) => {
    Modal.confirm({
      title: 'Xác nhận xóa',
      content: 'Bạn có chắc chắn muốn xóa sản phẩm này?',
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      onOk: async () => {
        await deleteProduct(id);
      },
    });
  };

  const handlePageChange = async (page: number, size?: number) => {
    const newPageSize = size || pageSize;
    setCurrentPage(page);
    setPageSize(newPageSize);
    await loadProducts({ page, limit: newPageSize });
  };

  const handleView = (product: Product) => {
    setSelectedProduct(product);
    setViewModalVisible(true);
  };

  const getFirstImage = (imageUrls?: string[]) => {
    if (!imageUrls || imageUrls.length === 0) return null;
    return imageUrls[0] || null;
  };

  const formatDateTime = (dateString?: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const handleTableChange = (
    _unused1: unknown,
    _unused2: unknown,
    sorter: SorterResult<Product> | SorterResult<Product>[]
  ) => {
    const sortObj = Array.isArray(sorter) ? sorter[0] : sorter;
    if (sortObj && sortObj.field) {
      setSortBy(sortObj.field as string);
      setSortOrder(sortObj.order === 'ascend' || sortObj.order === 'descend' ? sortObj.order : 'descend');
    }
  };

  const filteredProducts = products.filter(product => {
    // Filter by image availability
    if (filters.hideNoImage && (!product.imageUrls || product.imageUrls.length === 0)) {
      return false;
    }

    if (filters.showOnlyNoImage && product.imageUrls && product.imageUrls.length > 0) {
      return false;
    }

    // Filter by price range
    if (filters.priceRange !== 'all') {
      const price = typeof product.price === 'string'
        ? parseFloat(product.price.replace(/[^0-9.]/g, '')) || 0
        : product.price || 0;

      if (filters.priceRange === 'free' && price > 0) {
        return false;
      }

      if (filters.priceRange === 'paid' && price === 0) {
        return false;
      }
    }

    return true;
  });

  const getFilterStats = () => {
    const total = products.length;
    const withImages = products.filter(p => p.imageUrls && p.imageUrls.length > 0).length;
    const withoutImages = total - withImages;
    const freeProducts = products.filter(p => {
      const price = typeof p.price === 'string'
        ? parseFloat(p.price.replace(/[^0-9.]/g, '')) || 0
        : p.price || 0;
      return price === 0;
    }).length;
    const paidProducts = total - freeProducts;

    return { total, withImages, withoutImages, freeProducts, paidProducts };
  };

  const stats = getFilterStats();

  const columns = [
    {
      title: 'Hình ảnh',
      dataIndex: 'imageUrls',
      key: 'imageUrls',
      width: 100,
      render: (imageUrls: string[]) => {
        const imageSrc = getFirstImage(imageUrls);
        return (
          <Image
            width={60}
            height={60}
            src={imageSrc ?? undefined}
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
            style={{ objectFit: 'cover', borderRadius: 4 }}
          />
        );
      },
    },
    {
      title: 'Tên sản phẩm',
      dataIndex: 'name',
      key: 'name',
      sorter: true,
      sortOrder: sortBy === 'name' ? sortOrder : null,
      render: (text: string) => (
        <Typography.Text strong style={{ color: '#1890ff' }}>
          {text}
        </Typography.Text>
      ),
    },
    {
      title: 'Giá',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      sorter: true,
      sortOrder: sortBy === 'price' ? sortOrder : null,
      render: (price: string | number) => (
        <Tag color="green" style={{ fontSize: '14px', fontWeight: 'bold' }}>
          {typeof price === 'number' ? `${price}$` : price}
        </Tag>
      ),
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      ellipsis: {
        showTitle: false,
      },
      render: (description?: string) => (
        <Tooltip placement="topLeft" title={description || 'Không có mô tả'}>
          {description || 'Không có mô tả'}
        </Tooltip>
      ),
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      sorter: true,
      sortOrder: sortBy === 'createdAt' ? sortOrder : null,
      render: (createdAt?: string) => (
        <Typography.Text style={{ fontSize: '12px' }}>
          {formatDateTime(createdAt)}
        </Typography.Text>
      ),
    },
    {
      title: 'Ngày cập nhật',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 150,
      sorter: true,
      sortOrder: sortBy === 'updatedAt' ? sortOrder : null,
      render: (updatedAt?: string) => (
        <Typography.Text style={{ fontSize: '12px' }}>
          {formatDateTime(updatedAt)}
        </Typography.Text>
      ),
    },
    {
      title: 'Thao tác',
      key: 'action',
      width: 150,
      render: (_: unknown, record: Product) => (
        <Space size="small">
          <Tooltip title="Xem chi tiết">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          <Tooltip title="Chỉnh sửa">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => navigate(`/products/edit/${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="Xóa">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record.id!)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2} style={{ margin: 0 }}>
          Quản lý sản phẩm
        </Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => navigate('/products/add')}
          size="large"
        >
          Thêm sản phẩm mới
        </Button>
      </div>

      <div style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', gap: 16, alignItems: 'flex-start', marginBottom: 12 }}>
          <div style={{ flex: 1 }}>
            <Search
              placeholder="Tìm kiếm sản phẩm theo tên, mô tả, thành phần..."
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              onSearch={handleSearch}
              onChange={(e) => {
                if (!e.target.value) {
                  handleSearch('');
                }
              }}
            />
          </div>

          {/* Filter Controls */}
          <div style={{ display: 'flex', gap: 12, alignItems: 'center', flexWrap: 'wrap' }}>
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'all',
                    label: (
                      <div onClick={() => setFilters(prev => ({ ...prev, hideNoImage: false, showOnlyNoImage: false }))}>
                        <Space>
                          <span>Tất cả sản phẩm</span>
                          <Tag color="blue">{stats.total}</Tag>
                        </Space>
                      </div>
                    ),
                  },
                  {
                    key: 'with-images',
                    label: (
                      <div onClick={() => setFilters(prev => ({ ...prev, hideNoImage: true, showOnlyNoImage: false }))}>
                        <Space>
                          <span>Chỉ có hình ảnh</span>
                          <Tag color="green">{stats.withImages}</Tag>
                        </Space>
                      </div>
                    ),
                  },
                  {
                    key: 'no-images',
                    label: (
                      <div onClick={() => setFilters(prev => ({ ...prev, hideNoImage: false, showOnlyNoImage: true }))}>
                        <Space>
                          <span>Chưa có hình ảnh</span>
                          <Tag color="red">{stats.withoutImages}</Tag>
                        </Space>
                      </div>
                    ),
                  },
                ],
              }}
              trigger={['click']}
            >
              <Button icon={<FilterOutlined />}>
                Hình ảnh <DownOutlined />
              </Button>
            </Dropdown>

            <Dropdown
              menu={{
                items: [
                  {
                    key: 'all-price',
                    label: (
                      <div onClick={() => setFilters(prev => ({ ...prev, priceRange: 'all' }))}>
                        <Space>
                          <span>Tất cả giá</span>
                          <Tag color="blue">{stats.total}</Tag>
                        </Space>
                      </div>
                    ),
                  },
                  {
                    key: 'free',
                    label: (
                      <div onClick={() => setFilters(prev => ({ ...prev, priceRange: 'free' }))}>
                        <Space>
                          <span>Miễn phí</span>
                          <Tag color="green">{stats.freeProducts}</Tag>
                        </Space>
                      </div>
                    ),
                  },
                  {
                    key: 'paid',
                    label: (
                      <div onClick={() => setFilters(prev => ({ ...prev, priceRange: 'paid' }))}>
                        <Space>
                          <span>Có phí</span>
                          <Tag color="orange">{stats.paidProducts}</Tag>
                        </Space>
                      </div>
                    ),
                  },
                ],
              }}
              trigger={['click']}
            >
              <Button icon={<FilterOutlined />}>
                Giá <DownOutlined />
              </Button>
            </Dropdown>

            {(filters.hideNoImage || filters.showOnlyNoImage || filters.priceRange !== 'all') && (
              <Button
                icon={<ClearOutlined />}
                onClick={() => setFilters({ hideNoImage: false, showOnlyNoImage: false, priceRange: 'all' })}
                type="text"
                size="small"
              >
                Xóa filter
              </Button>
            )}
          </div>
        </div>

        {/* Filter Status */}
        {(filters.hideNoImage || filters.showOnlyNoImage || filters.priceRange !== 'all') && (
          <div style={{ marginBottom: 8 }}>
            <Space wrap>
              {filters.hideNoImage && (
                <Tag color="green" closable onClose={() => setFilters(prev => ({ ...prev, hideNoImage: false }))}>
                  Chỉ có hình ảnh
                </Tag>
              )}
              {filters.showOnlyNoImage && (
                <Tag color="red" closable onClose={() => setFilters(prev => ({ ...prev, showOnlyNoImage: false }))}>
                  Chưa có hình ảnh
                </Tag>
              )}
              {filters.priceRange === 'free' && (
                <Tag color="green" closable onClose={() => setFilters(prev => ({ ...prev, priceRange: 'all' }))}>
                  Miễn phí
                </Tag>
              )}
              {filters.priceRange === 'paid' && (
                <Tag color="orange" closable onClose={() => setFilters(prev => ({ ...prev, priceRange: 'all' }))}>
                  Có phí
                </Tag>
              )}
            </Space>
          </div>
        )}
      </div>

      <Table
        columns={columns}
        dataSource={filteredProducts}
        rowKey="id"
        loading={loading}
        pagination={false}
        scroll={{ x: 1200 }}
        onChange={handleTableChange}
      />

      {pagination && (
        <div style={{ marginTop: 16, textAlign: 'center' }}>
          <div style={{ marginBottom: 8, textAlign: 'left' }}>
            <Space wrap>
              <Typography.Text type="secondary">
                Hiển thị {filteredProducts.length} / {products.length} sản phẩm
              </Typography.Text>
              {filteredProducts.length !== products.length && (
                <Typography.Text type="warning" style={{ fontSize: '12px' }}>
                  (đã áp dụng filter)
                </Typography.Text>
              )}
            </Space>
          </div>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={pagination.total}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) =>
              `${range[0]}-${range[1]} của ${total} sản phẩm`
            }
            onChange={handlePageChange}
            onShowSizeChange={handlePageChange}
          />
        </div>
      )}

      {/* View Product Modal */}
      <Modal
        title="Chi tiết sản phẩm"
        open={viewModalVisible}
        onCancel={() => setViewModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setViewModalVisible(false)}>
            Đóng
          </Button>,
          <Button
            key="edit"
            type="primary"
            onClick={() => {
              setViewModalVisible(false);
              navigate(`/products/edit/${selectedProduct?.id}`);
            }}
          >
            Chỉnh sửa
          </Button>,
        ]}
        width={800}
      >
        {selectedProduct && (
          <div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
            <Card>
              <div style={{ marginBottom: 16 }}>
                <Image
                  width="100%"
                  height={200}
                  src={getFirstImage(selectedProduct.imageUrls) ?? undefined}
                  style={{ objectFit: 'cover', borderRadius: 8 }}
                />
              </div>

              <Title level={3}>{selectedProduct.name}</Title>
              <Tag color="green" style={{ fontSize: '16px', marginBottom: 16 }}>
                {typeof selectedProduct.price === 'number' ? `${selectedProduct.price}$` : selectedProduct.price}
              </Tag>

              {selectedProduct.description && (
                <div style={{ marginBottom: 12 }}>
                  <Typography.Text strong>Mô tả: </Typography.Text>
                  <Typography.Paragraph>{selectedProduct.description}</Typography.Paragraph>
                </div>
              )}

              <div style={{ marginBottom: 12 }}>
                <Typography.Text strong>Thành phần: </Typography.Text>
                <Typography.Paragraph>{selectedProduct.ingredients}</Typography.Paragraph>
              </div>

              <div style={{ marginBottom: 12 }}>
                <Typography.Text strong>Lợi ích: </Typography.Text>
                <Typography.Paragraph>{selectedProduct.benefits}</Typography.Paragraph>
              </div>

              <div style={{ marginBottom: 12 }}>
                <Typography.Text strong>Cách sử dụng: </Typography.Text>
                <Typography.Paragraph>{selectedProduct.usage}</Typography.Paragraph>
              </div>

              {selectedProduct.notes && (
                <div style={{ marginBottom: 12 }}>
                  <Typography.Text strong>Ghi chú: </Typography.Text>
                  <Typography.Paragraph>{selectedProduct.notes}</Typography.Paragraph>
                </div>
              )}
            </Card>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Products;
