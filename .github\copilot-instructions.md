# Copilot Custom Instructions for chatbot_admin_frontend

## Project Overview
This is a React (TypeScript) project using Vite and Ant Design (antd) for the admin frontend of a chatbot management system. The codebase is organized with clear separation of components, pages, hooks, services, and utilities.

## General Guidelines
- Use TypeScript for all new code.
- Follow the existing folder structure: place reusable UI in `src/components/ui`, pages in `src/pages`, hooks in `src/hooks`, and API logic in `src/services`.
- Use Ant Design components for UI consistency.
- Use functional components and React hooks.
- Prefer named exports for reusable modules.
- Use `src/constants` for shared constants and API endpoints.
- Use `src/types` for TypeScript type definitions.
- Use `src/utils` for utility/helper functions.

## UI/UX
- Maintain the modern, clean look provided by Ant Design.
- Use `Card`, `Table`, `Button`, `Tag`, and other Ant Design components as in current code.
- Keep UI responsive and accessible.

## State Management
- Use React context or hooks for local state.
- For global state, use the store in `src/store` if needed.

## API
- Place API calls in `src/services/apiClient.ts`.
- Use async/await and proper error handling.
- Mock data can be used for UI development, but real API integration should use the service layer.

## Code Style
- Follow the existing code style (indentation, semicolons, etc.).
- Use descriptive variable and function names in English.
- Add comments for complex logic.

## Testing
- Place tests alongside components/pages or in a dedicated `__tests__` folder if present.

## Miscellaneous
- Update or create documentation in `README.md` or `FRONTEND_STRUCTURE.md` as needed.
- Keep code modular and DRY (Don't Repeat Yourself).

---

These instructions are for GitHub Copilot and other AI coding assistants to ensure code consistency and maintainability in this project.
