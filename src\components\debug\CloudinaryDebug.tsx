import React from 'react';
import { Card, Typography, Tag, Space, Alert } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { cloudinaryService } from '../../services/cloudinaryService';

const { Title, Text, Paragraph } = Typography;

const CloudinaryDebug: React.FC = () => {
  const isConfigured = cloudinaryService.isConfigured();
  const cloudName = cloudinaryService.getCloudName();

  const envVars = {
    VITE_CLOUDINARY_URL: import.meta.env.VITE_CLOUDINARY_URL,
    VITE_CLOUDINARY_UPLOAD_PRESET: import.meta.env.VITE_CLOUDINARY_UPLOAD_PRESET,
  };

  return (
    <Card title="Cloudinary Configuration Debug" style={{ margin: 16 }}>
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        
        {/* Configuration Status */}
        <div>
          <Title level={4}>Configuration Status</Title>
          <Space>
            {isConfigured ? (
              <Tag icon={<CheckCircleOutlined />} color="success">
                Configured
              </Tag>
            ) : (
              <Tag icon={<CloseCircleOutlined />} color="error">
                Not Configured
              </Tag>
            )}
            {cloudName && (
              <Tag color="blue">
                Cloud: {cloudName}
              </Tag>
            )}
          </Space>
        </div>

        {/* Environment Variables */}
        <div>
          <Title level={4}>Environment Variables</Title>
          <Space direction="vertical" style={{ width: '100%' }}>
            {Object.entries(envVars).map(([key, value]) => (
              <div key={key} style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <Text strong style={{ minWidth: 250 }}>{key}:</Text>
                {value ? (
                  <Tag color="green">
                    {key.includes('URL') ? 
                      `${value.substring(0, 20)}...` : 
                      value
                    }
                  </Tag>
                ) : (
                  <Tag color="red">Not Set</Tag>
                )}
              </div>
            ))}
          </Space>
        </div>

        {/* Instructions */}
        <div>
          <Title level={4}>Setup Instructions</Title>
          {!isConfigured && (
            <Alert
              message="Cloudinary Not Configured"
              description={
                <div>
                  <Paragraph>
                    To configure Cloudinary, please follow these steps:
                  </Paragraph>
                  <ol>
                    <li>
                      <Text strong>Get Cloudinary credentials:</Text>
                      <br />
                      Visit <a href="https://cloudinary.com" target="_blank" rel="noopener noreferrer">cloudinary.com</a> and get your API credentials
                    </li>
                    <li>
                      <Text strong>Set environment variables:</Text>
                      <br />
                      <Text code>VITE_CLOUDINARY_URL=cloudinary://api_key:api_secret@cloud_name</Text>
                    </li>
                    <li>
                      <Text strong>Create upload preset:</Text>
                      <br />
                      In Cloudinary dashboard, go to Settings → Upload → Add upload preset
                      <br />
                      Set mode to "Unsigned" and name it "chatbot_admin"
                    </li>
                    <li>
                      <Text strong>Set upload preset:</Text>
                      <br />
                      <Text code>VITE_CLOUDINARY_UPLOAD_PRESET=chatbot_admin</Text>
                    </li>
                    <li>
                      <Text strong>Restart development server</Text>
                    </li>
                  </ol>
                </div>
              }
              type="warning"
              showIcon
              icon={<InfoCircleOutlined />}
            />
          )}
          
          {isConfigured && (
            <Alert
              message="Cloudinary Configured Successfully"
              description="You can now upload images to Cloudinary. The ImageUpload component will automatically use Cloudinary for storage."
              type="success"
              showIcon
            />
          )}
        </div>

        {/* Test Upload URL */}
        {isConfigured && (
          <div>
            <Title level={4}>Upload Endpoint</Title>
            <Text code>
              https://api.cloudinary.com/v1_1/{cloudName}/image/upload
            </Text>
          </div>
        )}

      </Space>
    </Card>
  );
};

export default CloudinaryDebug;
