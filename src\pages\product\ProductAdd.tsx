import React, { useState } from 'react';
import {
  Form,
  Input,
  Button,
  Card,
  Typography,
  message,
  Row,
  Col,
  Space,
  Divider,
  Modal,
  InputNumber
} from 'antd';
import { ArrowLeftOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ProductFormData, FAQ } from '../../types/product';
import { useProducts } from '../../hooks/useProducts';
import ImageUpload from '../../components/common/ImageUpload';
import CloudinaryDebug from '../../components/debug/CloudinaryDebug';

const { Title } = Typography;
const { TextArea } = Input;

const ProductAdd: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [faqModalVisible, setFaqModalVisible] = useState(false);
  const [faqForm] = Form.useForm();
  const [imageList, setImageList] = useState<string[]>([]);
  const navigate = useNavigate();

  const { createProduct } = useProducts();

  const handleSubmit = async (values: ProductFormData) => {
    setLoading(true);
    try {
      const productData: ProductFormData = {
        ...values,
        price: values.price, // Keep as number or string as per API
        images: imageList, // Use array format for API
        faqs: faqs
      };

      const result = await createProduct(productData);
      if (result) {
        navigate('/products');
      }
    } catch (error) {
      message.error('Không thể thêm sản phẩm');
      console.error('Error creating product:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddFaq = () => {
    faqForm.validateFields().then((values) => {
      setFaqs([...faqs, values]);
      faqForm.resetFields();
      setFaqModalVisible(false);
      message.success('Thêm FAQ thành công');
    });
  };

  const handleDeleteFaq = (index: number) => {
    const newFaqs = faqs.filter((_, i) => i !== index);
    setFaqs(newFaqs);
    message.success('Xóa FAQ thành công');
  };



  return (
    <div>
      {/* Debug component - only show in development */}
      {import.meta.env.DEV && <CloudinaryDebug />}

      <div style={{ marginBottom: 24 }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/products')}
          style={{ marginBottom: 16 }}
        >
          Quay lại danh sách
        </Button>
        <Title level={2}>Thêm sản phẩm mới</Title>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        scrollToFirstError
      >
        <Row gutter={24}>
          <Col span={24}>
            <Card title="Thông tin cơ bản" style={{ marginBottom: 24 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="product_name"
                    label="Tên sản phẩm"
                    rules={[{ required: true, message: 'Vui lòng nhập tên sản phẩm' }]}
                  >
                    <Input placeholder="Nhập tên sản phẩm" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="price"
                    label="Giá ($)"
                    rules={[{ required: true, message: 'Vui lòng nhập giá sản phẩm' }]}
                  >
                    <InputNumber
                      placeholder="Ví dụ: 20.99"
                      min={0}
                      precision={2}
                      step={0.01}
                      style={{ width: '100%' }}
                      formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={(value) => value ? Number(value.replace(/\$\s?|(,*)/g, '')) : 0}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="description"
                label="Mô tả sản phẩm"
              >
                <TextArea
                  rows={4}
                  placeholder="Nhập mô tả chi tiết về sản phẩm"
                />
              </Form.Item>

              <Form.Item
                label="Hình ảnh sản phẩm"
                required
              >
                <ImageUpload
                  value={imageList}
                  onChange={setImageList}
                  maxCount={5}
                  folder="products"
                />
              </Form.Item>
            </Card>
          </Col>

          <Col span={24}>
            <Card title="Thông tin chi tiết" style={{ marginBottom: 24 }}>
              <Form.Item
                name="ingredients"
                label="Thành phần"
                rules={[{ required: true, message: 'Vui lòng nhập thành phần' }]}
              >
                <TextArea
                  rows={3}
                  placeholder="Nhập các thành phần của sản phẩm"
                />
              </Form.Item>

              <Form.Item
                name="benefits"
                label="Lợi ích"
                rules={[{ required: true, message: 'Vui lòng nhập lợi ích' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="Nhập các lợi ích của sản phẩm"
                />
              </Form.Item>

              <Form.Item
                name="usage"
                label="Cách sử dụng"
                rules={[{ required: true, message: 'Vui lòng nhập cách sử dụng' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="Nhập hướng dẫn sử dụng sản phẩm"
                />
              </Form.Item>

              <Form.Item
                name="instructions"
                label="Hướng dẫn bảo quản"
                rules={[{ required: true, message: 'Vui lòng nhập hướng dẫn bảo quản' }]}
              >
                <TextArea
                  rows={3}
                  placeholder="Nhập hướng dẫn bảo quản sản phẩm"
                />
              </Form.Item>
            </Card>
          </Col>

          <Col span={24}>
            <Card title="Thông tin bổ sung" style={{ marginBottom: 24 }}>
              <Form.Item
                name="targetUser"
                label="Đối tượng sử dụng"
              >
                <TextArea
                  rows={3}
                  placeholder="Nhập đối tượng phù hợp sử dụng sản phẩm"
                />
              </Form.Item>

              <Form.Item
                name="purpose"
                label="Mục đích sử dụng"
              >
                <TextArea
                  rows={3}
                  placeholder="Nhập mục đích sử dụng sản phẩm"
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="notes"
                    label="Ghi chú"
                  >
                    <TextArea
                      rows={3}
                      placeholder="Nhập ghi chú bổ sung"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="otherInfo"
                    label="Thông tin khác"
                  >
                    <TextArea
                      rows={3}
                      placeholder="Nhập thông tin bổ sung khác"
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>

          <Col span={24}>
            <Card 
              title="Câu hỏi thường gặp (FAQ)"
              extra={
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setFaqModalVisible(true)}
                >
                  Thêm FAQ
                </Button>
              }
              style={{ marginBottom: 24 }}
            >
              {faqs.length === 0 ? (
                <div style={{ textAlign: 'center', color: '#999', padding: '20px 0' }}>
                  Chưa có câu hỏi nào. Nhấn "Thêm FAQ" để thêm câu hỏi.
                </div>
              ) : (
                <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
                  {faqs.map((item, index) => (
                    <Card
                      key={index}
                      size="small"
                      style={{ border: '1px solid #f0f0f0' }}
                      actions={[
                        <Button
                          key="delete"
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleDeleteFaq(index)}
                        >
                          Xóa
                        </Button>
                      ]}
                    >
                      <div>
                        <div style={{ fontWeight: 'bold', marginBottom: 8, color: '#1890ff' }}>
                          Q: {item.question}
                        </div>
                        <div style={{ color: '#666' }}>
                          A: {item.answer}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </Card>
          </Col>
        </Row>

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Space size="large">
            <Button size="large" onClick={() => navigate('/products')}>
              Hủy
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              size="large"
            >
              Thêm sản phẩm
            </Button>
          </Space>
        </div>
      </Form>

      {/* FAQ Modal */}
      <Modal
        title="Thêm câu hỏi thường gặp"
        open={faqModalVisible}
        onOk={handleAddFaq}
        onCancel={() => {
          setFaqModalVisible(false);
          faqForm.resetFields();
        }}
        okText="Thêm"
        cancelText="Hủy"
      >
        <Form form={faqForm} layout="vertical">
          <Form.Item
            name="question"
            label="Câu hỏi"
            rules={[{ required: true, message: 'Vui lòng nhập câu hỏi' }]}
          >
            <Input placeholder="Nhập câu hỏi" />
          </Form.Item>
          <Form.Item
            name="answer"
            label="Câu trả lời"
            rules={[{ required: true, message: 'Vui lòng nhập câu trả lời' }]}
          >
            <TextArea rows={4} placeholder="Nhập câu trả lời" />
          </Form.Item>
        </Form>
      </Modal>


    </div>
  );
};

export default ProductAdd;
