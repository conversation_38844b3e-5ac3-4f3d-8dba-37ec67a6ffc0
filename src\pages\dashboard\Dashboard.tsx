import React from 'react'
import { <PERSON>, Row, Col, Statistic } from 'antd'
import { RobotOutlined, MessageOutlined, UserOutlined, ClockCircleOutlined } from '@ant-design/icons'

const Dashboard: React.FC = () => {
  return (
    <div>
      <h2 style={{ color: '#1890ff', marginBottom: '24px' }}>Dashboard</h2>
      
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Chatbots"
              value={12}
              prefix={<RobotOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Active Conversations"
              value={45}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Users"
              value={1234}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Uptime"
              value="99.9%"
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
      </Row>

      <div style={{ marginTop: '24px' }}>
        <Card title="System Overview" style={{ marginBottom: '16px' }}>
          <p>Welcome to the Chatbot Admin Dashboard! Here you can monitor your chatbot system's performance and key metrics.</p>
          <ul>
            <li>Monitor real-time chatbot activity</li>
            <li>Track conversation statistics</li>
            <li>View system health metrics</li>
            <li>Manage user interactions</li>
          </ul>
        </Card>
      </div>
    </div>
  )
}

export default Dashboard
