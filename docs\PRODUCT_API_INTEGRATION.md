# Product API Integration

This document describes the integration of the Product API with the frontend application.

## Overview

The application now supports both mock data (localStorage) and real API integration for product management. The system uses a service factory pattern to switch between implementations based on configuration.

## Configuration

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# API Configuration
VITE_API_BASE_URL=http://localhost:3000
VITE_USE_MOCK_API=false
VITE_ENABLE_PRODUCT_API=true

# Development settings
VITE_DEBUG_MODE=true
```

### Configuration Options

- `VITE_USE_MOCK_API`: Set to `true` to use localStorage mock data, `false` to use real API
- `VITE_API_BASE_URL`: Base URL for the API server
- `VITE_ENABLE_PRODUCT_API`: Feature flag to enable/disable product API features

## Architecture

### Service Layer

1. **productApiService.ts**: Real API implementation using axios
2. **productService.ts**: Mock implementation using localStorage
3. **productServiceFactory.ts**: Factory that switches between implementations
4. **apiClient.ts**: HTTP client with interceptors for authentication and error handling

### Hooks

1. **useProducts**: Hook for managing product lists with pagination and search
2. **useProduct**: Hook for managing individual product operations

### Components

The existing product components (`Products.tsx`, `ProductAdd.tsx`, `ProductEdit.tsx`) have been updated to use the new hooks and data structures.

## API Endpoints

Based on `api_structure/products-api.md`:

- `GET /api/products` - Get paginated product list
- `GET /api/products/:id` - Get single product
- `POST /api/products` - Create new product
- `PUT /api/products/:id` - Update product (full)
- `PATCH /api/products/:id` - Update product (partial)
- `DELETE /api/products/:id` - Delete product
- `POST /api/products/bulk-import` - Bulk import products

## Data Structure Changes

### Old Format (Mock)
```typescript
interface Product {
  id?: string;
  product_name: string;
  notes: string;
  price: string;
  image: string; // comma-separated URLs
  // ... other fields
}
```

### New Format (API)
```typescript
interface Product {
  id?: number;
  name: string;
  description?: string;
  price: number | string;
  imageUrls?: string[]; // array of URLs
  // ... other fields
}
```

## Usage Examples

### Using the Hooks

```typescript
// List products with pagination
const { products, loading, pagination, loadProducts } = useProducts({
  page: 1,
  limit: 10
});

// Get single product
const { product, loading, updateProduct } = useProduct(productId);

// Create product
const { createProduct } = useProducts();
const newProduct = await createProduct(productData);
```

### Switching Between Mock and API

To use mock data (for development/testing):
```env
VITE_USE_MOCK_API=true
```

To use real API:
```env
VITE_USE_MOCK_API=false
VITE_API_BASE_URL=http://localhost:3000
```

## Testing

1. **Mock Mode**: Uses localStorage, good for development without backend
2. **API Mode**: Connects to real backend server

## Error Handling

- Network errors are handled by axios interceptors
- Authentication errors trigger token refresh or redirect to login
- User-friendly error messages are displayed via Ant Design message component

## Migration Notes

The system maintains backward compatibility by using adapters that convert between old and new data formats. This allows for gradual migration from mock to real API.

## Future Improvements

1. Add caching layer for better performance
2. Implement optimistic updates
3. Add offline support
4. Implement real-time updates via WebSocket
