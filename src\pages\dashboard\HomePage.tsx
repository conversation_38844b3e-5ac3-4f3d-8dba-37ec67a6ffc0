import React from 'react';
import { <PERSON>, <PERSON>, Card, Statistic, Typography, Button, Space, Progress, List, Avatar } from 'antd';
import { 
  RobotOutlined, 
  MessageOutlined, 
  UserOutlined, 
  RiseOutlined,
  PlusOutlined,
  EyeOutlined 
} from '@ant-design/icons';

const { Title, Text } = Typography;

const HomePage: React.FC = () => {
  // Mock data
  const recentActivities = [
    {
      title: 'New chatbot "Support Bot" created',
      description: '2 minutes ago',
      avatar: <RobotOutlined style={{ color: '#1890ff' }} />,
    },
    {
      title: 'Conversation with user #123 completed',
      description: '15 minutes ago',
      avatar: <MessageOutlined style={{ color: '#52c41a' }} />,
    },
    {
      title: 'System maintenance scheduled',
      description: '1 hour ago',
      avatar: <UserOutlined style={{ color: '#faad14' }} />,
    },
  ];

  return (
    <div>
      <Title level={2}>Dashboard</Title>
      <Text type="secondary" style={{ marginBottom: 24, display: 'block' }}>
        Welcome to the chatbot administration panel. Here's an overview of your system.
      </Text>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Chatbots"
              value={12}
              prefix={<RobotOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Active Conversations"
              value={248}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Users"
              value={1.2}
              suffix="K"
              prefix={<UserOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Success Rate"
              value={98.5}
              suffix="%"
              prefix={<RiseOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card 
            title="System Performance" 
            extra={<Button type="text" icon={<EyeOutlined />}>View Details</Button>}
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Text strong>CPU Usage</Text>
                <Progress percent={65} status="active" />
              </Col>
              <Col span={12}>
                <Text strong>Memory Usage</Text>
                <Progress percent={42} />
              </Col>
              <Col span={12}>
                <Text strong>Response Time</Text>
                <Progress percent={88} status="active" />
              </Col>
              <Col span={12}>
                <Text strong>Uptime</Text>
                <Progress percent={99} />
              </Col>
            </Row>
          </Card>

          <Card 
            title="Quick Actions" 
            style={{ marginTop: 16 }}
          >
            <Space wrap>
              <Button type="primary" icon={<PlusOutlined />}>
                Create New Chatbot
              </Button>
              <Button icon={<EyeOutlined />}>
                View All Chatbots
              </Button>
              <Button icon={<MessageOutlined />}>
                View Conversations
              </Button>
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="Recent Activity">
            <List
              itemLayout="horizontal"
              dataSource={recentActivities}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={item.avatar} />}
                    title={item.title}
                    description={item.description}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default HomePage;
