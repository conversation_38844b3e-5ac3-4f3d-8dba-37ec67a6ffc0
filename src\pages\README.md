# Pages Structure

Cấu trúc thư mục pages được tổ chức theo chức năng để dễ quản lý và bảo trì.

## 📁 Cấu trúc thư mục

```
src/pages/
├── auth/                    # X<PERSON><PERSON> thực & <PERSON><PERSON><PERSON> nhập
│   ├── Login.tsx           # Màn hình đăng nhập
│   └── index.ts            # Export auth components
│
├── dashboard/              # Trang chủ & Dashboard
│   ├── Dashboard.tsx       # Màn hình dashboard chính
│   ├── HomePage.tsx        # Trang chủ
│   └── index.ts            # Export dashboard components
│
├── product/                # Quản lý sản phẩm
│   ├── Products.tsx        # Danh sách sản phẩm
│   ├── ProductAdd.tsx      # Thêm sản phẩm mới
│   ├── ProductEdit.tsx     # Chỉnh sửa sản phẩm
│   └── index.ts            # Export product components
│
├── chatbot/                # Quản lý Chatbot
│   ├── Chatbots.tsx        # Danh sách chatbot
│   ├── ChatbotsPage.tsx    # Trang chatbot chi tiết
│   └── index.ts            # Export chatbot components
│
├── conversation/           # Quản lý Hội thoại
│   ├── Conversations.tsx   # Danh sách hội thoại
│   ├── ConversationsPage.tsx # Trang hội thoại chi tiết
│   └── index.ts            # Export conversation components
│
├── setting/                # Cài đặt hệ thống
│   ├── Settings.tsx        # Cài đặt chung
│   ├── SettingsPage.tsx    # Trang cài đặt chi tiết
│   └── index.ts            # Export setting components
│
└── index.ts                # Export tất cả pages
```

## 🎯 Nguyên tắc tổ chức

### 1. **Nhóm theo chức năng**
- Mỗi thư mục con đại diện cho một chức năng chính của ứng dụng
- Các màn hình liên quan được gom chung một thư mục

### 2. **Export pattern**
- Mỗi thư mục có file `index.ts` để export các component
- File `index.ts` chính export tất cả từ các thư mục con
- Import dễ dàng: `import { Products, ProductAdd } from './pages'`

### 3. **Naming convention**
- Thư mục: lowercase (product, chatbot, conversation)
- Component: PascalCase (Products.tsx, ProductAdd.tsx)
- Export: PascalCase (Products, ProductAdd)

## 📝 Cách sử dụng

### Import components:
```typescript
// Import từ index chính
import { 
  Login,           // từ auth/
  Dashboard,       // từ dashboard/
  Products,        // từ product/
  ProductAdd,      // từ product/
  Chatbots,        // từ chatbot/
  Conversations,   // từ conversation/
  Settings         // từ setting/
} from './pages';

// Hoặc import trực tiếp từ thư mục
import { Products, ProductAdd } from './pages/product';
```

### Thêm component mới:
1. Tạo file component trong thư mục phù hợp
2. Export trong file `index.ts` của thư mục đó
3. Component sẽ tự động available qua main index

## 🔄 Migration từ cấu trúc cũ

Các file đã được di chuyển từ `src/pages/` flat structure sang nested structure:

- `Login.tsx` → `auth/Login.tsx`
- `Dashboard.tsx` → `dashboard/Dashboard.tsx`
- `Products.tsx` → `product/Products.tsx`
- `ProductAdd.tsx` → `product/ProductAdd.tsx`
- `ProductEdit.tsx` → `product/ProductEdit.tsx`
- `Chatbots.tsx` → `chatbot/Chatbots.tsx`
- `Conversations.tsx` → `conversation/Conversations.tsx`
- `Settings.tsx` → `setting/Settings.tsx`

Import paths đã được cập nhật tương ứng trong `App.tsx` và các file liên quan.
