import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { productServiceInstance } from '../services/productServiceFactory';
import type { Product, ProductFormData } from '../types/product';

interface UseProductState {
  product: Product | null;
  loading: boolean;
  error: string | null;
}

interface UseProductReturn extends UseProductState {
  loadProduct: (id: string | number) => Promise<void>;
  updateProduct: (productData: ProductFormData) => Promise<Product | null>;
  patchProduct: (productData: Partial<ProductFormData>) => Promise<Product | null>;
  deleteProduct: () => Promise<boolean>;
  refresh: () => Promise<void>;
}

export function useProduct(productId?: string | number): UseProductReturn {
  const [state, setState] = useState<UseProductState>({
    product: null,
    loading: false,
    error: null,
  });

  const loadProduct = useCallback(async (id: string | number) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const product = await productServiceInstance.getProductById(id);

      if (product) {
        setState(prev => ({
          ...prev,
          product: product,
          loading: false,
        }));
      } else {
        throw new Error('Product not found');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load product';
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      message.error(errorMessage);
    }
  }, []);

  const updateProduct = useCallback(async (productData: ProductFormData): Promise<Product | null> => {
    if (!state.product?.id) {
      message.error('No product ID available');
      return null;
    }

    try {
      const result = await productServiceInstance.updateProduct(state.product.id, productData);
      message.success('Cập nhật sản phẩm thành công');
      setState(prev => ({
        ...prev,
        product: result,
      }));
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update product';
      message.error(errorMessage);
      return null;
    }
  }, [state.product?.id]);

  const patchProduct = useCallback(async (productData: Partial<ProductFormData>): Promise<Product | null> => {
    if (!state.product?.id) {
      message.error('No product ID available');
      return null;
    }

    try {
      // For now, use the full update method since the mock service doesn't support patch
      const fullData = { ...state.product, ...productData } as ProductFormData;
      const result = await productServiceInstance.updateProduct(state.product.id, fullData);
      message.success('Cập nhật sản phẩm thành công');
      setState(prev => ({
        ...prev,
        product: result,
      }));
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update product';
      message.error(errorMessage);
      return null;
    }
  }, [state.product]);

  const deleteProduct = useCallback(async (): Promise<boolean> => {
    if (!state.product?.id) {
      message.error('No product ID available');
      return false;
    }

    try {
      const result = await productServiceInstance.deleteProduct(state.product.id);

      if (result) {
        message.success('Xóa sản phẩm thành công');
        setState(prev => ({
          ...prev,
          product: null,
        }));
      }
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete product';
      message.error(errorMessage);
      return false;
    }
  }, [state.product?.id]);

  const refresh = useCallback(async () => {
    if (state.product?.id) {
      await loadProduct(state.product.id);
    }
  }, [loadProduct, state.product?.id]);

  // Load product on mount if productId is provided
  useEffect(() => {
    if (productId) {
      loadProduct(productId);
    }
  }, [loadProduct, productId]);

  return {
    ...state,
    loadProduct,
    updateProduct,
    patchProduct,
    deleteProduct,
    refresh,
  };
}
