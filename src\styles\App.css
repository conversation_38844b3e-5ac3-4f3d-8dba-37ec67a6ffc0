/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow: hidden; /* Prevent double scrollbars */
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f5f5f5;
}

#root {
  height: 100vh;
  overflow: hidden;
}

/* Layout styles */
.ant-layout {
  height: 100vh;
  background-color: #f5f5f5;
}

.ant-layout-sider {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
  z-index: 1001;
  height: 100vh !important;
}

.ant-layout-header {
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  z-index: 1000;
  position: sticky !important;
  top: 0;
  height: 64px;
  line-height: 64px;
}

.ant-layout-content {
  background-color: #f5f5f5;
  height: calc(100vh - 64px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* Card styles */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
  padding: 16px 24px;
}

.ant-card-body {
  padding: 24px;
}

/* Button styles */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.016);
}

.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.ant-btn:focus {
  outline: none;
}

/* Table styles */
.ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #f0f0f0;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* Form styles */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input, .ant-input-number, .ant-select-selector {
  border-radius: 6px;
}

.ant-input:focus, .ant-input-number:focus, .ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Statistic styles */
.ant-statistic-title {
  color: #8c8c8c;
  font-weight: 500;
}

.ant-statistic-content {
  font-weight: 600;
}

/* Progress styles */
.ant-progress-text {
  font-weight: 500;
}

/* Modal styles */
.ant-modal {
  border-radius: 8px;
}

.ant-modal-header {
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-body {
  padding: 24px;
}

/* Tag styles */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
  border: none;
}

/* Menu styles */
.ant-menu-dark {
  background-color: #001529;
}

.ant-menu-dark .ant-menu-item-selected {
  background-color: #1890ff !important;
}

.ant-menu-dark .ant-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Dropdown styles */
.ant-dropdown {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Custom utility classes */
.text-center {
  text-align: center;
}

.mb-24 {
  margin-bottom: 24px !important;
}

.mb-16 {
  margin-bottom: 16px !important;
}

.mt-16 {
  margin-top: 16px !important;
}

.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects */
.hover-effect {
  transition: all 0.3s ease;
}

.hover-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Responsive styles */
@media (max-width: 992px) {
  .ant-layout-sider {
    position: fixed !important;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 1002;
  }
  
  .ant-layout {
    margin-left: 0 !important;
  }
  
  .ant-layout-content {
    margin: 16px 8px !important;
    padding: 16px !important;
  }
}

@media (max-width: 768px) {
  .ant-card-body {
    padding: 16px;
  }
  
  .ant-table-wrapper {
    overflow-x: auto;
  }
  
  .ant-col {
    margin-bottom: 16px;
  }
  
  .ant-layout-content {
    margin: 12px 6px !important;
    padding: 12px !important;
  }
}

@media (max-width: 576px) {
  .ant-layout-content {
    margin: 8px 4px !important;
    padding: 8px !important;
  }
  
  .ant-card-head {
    padding: 12px 16px;
  }
  
  .ant-card-body {
    padding: 12px;
  }
}
