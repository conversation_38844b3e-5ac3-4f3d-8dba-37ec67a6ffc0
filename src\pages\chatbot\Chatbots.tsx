import React from 'react'
import { <PERSON>, Table, Button, Space, Tag, Popconfirm } from 'antd'
import { EditOutlined, DeleteOutlined, PlusOutlined, PlayCircleOutlined, PauseCircleOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'

interface ChatbotData {
  key: string
  name: string
  status: 'active' | 'inactive'
  conversations: number
  lastUpdated: string
}

const Chatbots: React.FC = () => {
  const mockData: ChatbotData[] = [
    {
      key: '1',
      name: 'Customer Support Bot',
      status: 'active',
      conversations: 234,
      lastUpdated: '2025-01-15 10:30'
    },
    {
      key: '2',
      name: 'Sales Assistant Bot',
      status: 'active',
      conversations: 156,
      lastUpdated: '2025-01-14 16:45'
    },
    {
      key: '3',
      name: 'FAQ Bot',
      status: 'inactive',
      conversations: 89,
      lastUpdated: '2025-01-13 09:15'
    },
    {
      key: '4',
      name: 'Technical Support Bot',
      status: 'active',
      conversations: 67,
      lastUpdated: '2025-01-15 14:20'
    }
  ]

  const columns: ColumnsType<ChatbotData> = [
    {
      title: 'Chatbot Name',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <strong>{text}</strong>
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Conversations',
      dataIndex: 'conversations',
      key: 'conversations',
      sorter: (a, b) => a.conversations - b.conversations
    },
    {
      title: 'Last Updated',
      dataIndex: 'lastUpdated',
      key: 'lastUpdated'
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="middle">
          <Button
            size="small"
            icon={record.status === 'active' ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            onClick={() => console.log('Toggle status:', record.key)}
          >
            {record.status === 'active' ? 'Pause' : 'Start'}
          </Button>
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={() => console.log('Edit:', record.key)}
          >
            Edit
          </Button>
          <Popconfirm
            title="Are you sure you want to delete this chatbot?"
            onConfirm={() => console.log('Delete:', record.key)}
            okText="Yes"
            cancelText="No"
          >
            <Button size="small" danger icon={<DeleteOutlined />}>
              Delete
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <h2 style={{ color: '#1890ff', margin: 0 }}>Chatbots</h2>
        <Button type="primary" icon={<PlusOutlined />}>
          Create New Chatbot
        </Button>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={mockData}
          pagination={{ pageSize: 10 }}
          size="middle"
        />
      </Card>

      <div style={{ marginTop: '24px' }}>
        <Card title="Chatbot Management" size="small">
          <p>Manage your chatbots from this interface:</p>
          <ul>
            <li>Create and configure new chatbots</li>
            <li>Start/stop chatbot services</li>
            <li>Edit chatbot settings and responses</li>
            <li>Monitor chatbot performance and conversations</li>
            <li>Delete unused or outdated chatbots</li>
          </ul>
        </Card>
      </div>
    </div>
  )
}

export default Chatbots
