import React from 'react'
import { Card, Table, Tag, Button, Space, Input } from 'antd'
import { SearchOutlined, EyeOutlined, MessageOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'

const { Search } = Input

interface ConversationData {
  key: string
  id: string
  user: string
  chatbot: string
  status: 'active' | 'completed' | 'pending'
  messages: number
  startTime: string
  lastActivity: string
}

const Conversations: React.FC = () => {
  const mockData: ConversationData[] = [
    {
      key: '1',
      id: 'CONV-001',
      user: '<PERSON>',
      chatbot: 'Customer Support Bot',
      status: 'active',
      messages: 15,
      startTime: '2025-01-15 10:30',
      lastActivity: '2 minutes ago'
    },
    {
      key: '2',
      id: 'CONV-002',
      user: '<PERSON>',
      chatbot: 'Sales Assistant Bot',
      status: 'completed',
      messages: 8,
      startTime: '2025-01-15 09:45',
      lastActivity: '1 hour ago'
    },
    {
      key: '3',
      id: 'CONV-003',
      user: '<PERSON>',
      chatbot: 'Technical Support Bot',
      status: 'pending',
      messages: 3,
      startTime: '2025-01-15 14:20',
      lastActivity: '5 minutes ago'
    },
    {
      key: '4',
      id: 'CONV-004',
      user: 'Sarah Williams',
      chatbot: 'FAQ Bot',
      status: 'completed',
      messages: 12,
      startTime: '2025-01-15 08:15',
      lastActivity: '3 hours ago'
    }
  ]

  const columns: ColumnsType<ConversationData> = [
    {
      title: 'Conversation ID',
      dataIndex: 'id',
      key: 'id',
      render: (text) => <code>{text}</code>
    },
    {
      title: 'User',
      dataIndex: 'user',
      key: 'user'
    },
    {
      title: 'Chatbot',
      dataIndex: 'chatbot',
      key: 'chatbot'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: 'active' | 'completed' | 'pending') => {
        const colorMap: Record<'active' | 'completed' | 'pending', string> = {
          active: 'blue',
          completed: 'green',
          pending: 'orange'
        }
        return (
          <Tag color={colorMap[status]}>
            {status.toUpperCase()}
          </Tag>
        )
      }
    },
    {
      title: 'Messages',
      dataIndex: 'messages',
      key: 'messages',
      sorter: (a, b) => a.messages - b.messages
    },
    {
      title: 'Start Time',
      dataIndex: 'startTime',
      key: 'startTime'
    },
    {
      title: 'Last Activity',
      dataIndex: 'lastActivity',
      key: 'lastActivity'
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="middle">
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => console.log('View conversation:', record.key)}
          >
            View
          </Button>
          <Button
            size="small"
            icon={<MessageOutlined />}
            onClick={() => console.log('Join conversation:', record.key)}
            disabled={record.status !== 'active'}
          >
            Join
          </Button>
        </Space>
      )
    }
  ]

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <h2 style={{ color: '#1890ff', margin: 0 }}>Conversations</h2>
        <Search
          placeholder="Search conversations..."
          allowClear
          style={{ width: 300 }}
          prefix={<SearchOutlined />}
        />
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={mockData}
          pagination={{ pageSize: 10 }}
          size="middle"
        />
      </Card>

      <div style={{ marginTop: '24px' }}>
        <Card title="Conversation Monitoring" size="small">
          <p>Monitor and analyze conversations between users and chatbots:</p>
          <ul>
            <li>View real-time active conversations</li>
            <li>Review completed conversation histories</li>
            <li>Join ongoing conversations as needed</li>
            <li>Analyze conversation patterns and user satisfaction</li>
            <li>Export conversation data for reporting</li>
          </ul>
        </Card>
      </div>
    </div>
  )
}

export default Conversations
