# Frontend Structure Guide

## 📁 Folder Structure

```
src/
├── assets/           # Static assets (images, icons, etc.)
├── components/       # React components
│   ├── common/       # Common components used across pages
│   └── ui/           # Reusable UI components
├── pages/            # Page components (routes)
├── hooks/            # Custom React hooks
├── services/         # API services and HTTP clients
├── utils/            # Utility functions and helpers
├── types/            # TypeScript type definitions
├── store/            # State management (Redux/Zustand/Context)
├── styles/           # Global styles and CSS modules
├── constants/        # Application constants
└── contexts/         # React Context providers
```

## 🧩 Component Organization

### UI Components (`components/ui/`)
- Reusable, generic components
- Should not contain business logic
- Example: But<PERSON>, In<PERSON>, <PERSON><PERSON>, Card

### Common Components (`components/common/`)
- Components used across multiple pages
- May contain some business logic
- Example: Header, Footer, Sidebar, Navigation

### Pages (`pages/`)
- Top-level route components
- Compose smaller components
- Handle page-specific logic

## 🎯 Best Practices

### 1. Import/Export Strategy
```typescript
// Use index files for clean imports
import { But<PERSON>, <PERSON>dal } from '@/components/ui';
import { HomePage, DashboardPage } from '@/pages';
```

### 2. TypeScript Types
```typescript
// Define interfaces in types/
interface User {
  id: string;
  name: string;
  email: string;
}
```

### 3. API Services
```typescript
// Centralize API calls
const userService = {
  getUser: (id: string) => apiClient.get(`/users/${id}`),
  updateUser: (id: string, data: Partial<User>) => 
    apiClient.put(`/users/${id}`, data)
};
```

### 4. Custom Hooks
```typescript
// Extract reusable logic
const useUser = (id: string) => {
  const { data, loading, error, execute } = useApi(
    () => userService.getUser(id)
  );
  
  useEffect(() => {
    execute();
  }, [id, execute]);
  
  return { user: data, loading, error };
};
```

### 5. Component Props
```typescript
// Use proper TypeScript interfaces for props
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
}
```

## 📝 Naming Conventions

- **Components**: PascalCase (e.g., `UserProfile.tsx`)
- **Files/Folders**: camelCase (e.g., `userService.ts`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_BASE_URL`)
- **Types/Interfaces**: PascalCase (e.g., `interface UserData`)

## 🔧 Usage Examples

### Creating a new page
```typescript
// pages/UsersPage.tsx
import React from 'react';
import { Button } from '@/components/ui';
import { useUsers } from '@/hooks';

const UsersPage: React.FC = () => {
  const { users, loading } = useUsers();
  
  return (
    <div>
      <h1>Users</h1>
      {loading ? (
        <p>Loading...</p>
      ) : (
        users?.map(user => <div key={user.id}>{user.name}</div>)
      )}
    </div>
  );
};

export default UsersPage;
```

### Creating a service
```typescript
// services/userService.ts
import { apiClient } from './apiClient';
import type { User, ApiResponse } from '@/types';

export const userService = {
  getUsers: (): Promise<ApiResponse<User[]>> =>
    apiClient.get('/users'),
    
  getUser: (id: string): Promise<ApiResponse<User>> =>
    apiClient.get(`/users/${id}`),
    
  createUser: (userData: Omit<User, 'id'>): Promise<ApiResponse<User>> =>
    apiClient.post('/users', userData),
};
```

This structure provides:
- ✅ **Scalability**: Easy to add new features
- ✅ **Maintainability**: Clear separation of concerns
- ✅ **Type Safety**: Full TypeScript support
- ✅ **Reusability**: Modular component design
- ✅ **Developer Experience**: Clean imports and exports
